﻿@import url(font.css);

:root {
  --main-bg-color: rgb(248, 246, 231);
  --text-color: #1a1717;
  --accent-color: #b7b2a9;
  --button-color: #f6dc50;
  --header-padding: 20px 10%;
  --logo-color: #000000;
  --nav-link-color: #b4b4b4;
  --nav-link-hover-color: #797572;
  --button-text-color: #1f242d;
  --shadow-color: #85837f;
  --font-primary: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 24px;
  --font-size-2xl: 32px;
  --font-size-3xl: 48px;
  --line-height-base: 1.6;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
}

.dark-mode {
  --main-bg-color: #1a1a1a;
  --text-color: #e0e0e0;
  --accent-color: #6b6b6b;
  --button-color: #ffd700;
  --logo-color: #ffffff;
  --nav-link-color: #a0a0a0;
  --nav-link-hover-color: #ffffff;
  --button-text-color: #000000;
  --shadow-color: #4a4a4a;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  background: var(--main-bg-color);
  color: var(--text-color);
  transition: background-color 0.3s, color 0.3s;
}

.header, .header2 {
  width: 100%;
  padding: var(--header-padding);
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
}

.logo {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-semibold);
  color: var(--logo-color);
  text-decoration: none;
  cursor: default;
  opacity: 0;
  animation: slideRight 1s ease forwards;
}

.navbar a {
  display: inline-block;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--nav-link-color);
  text-decoration: none;
  margin-left: 35px;
  transition: 0.3s;
  opacity: 0;
  animation: slideTop 1s ease forwards;
  animation-delay: calc(.2s * var(--i));
}

.navbar a:hover,
.navbar a.active {
  color: var(--nav-link-hover-color);
}

.home {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10%;
}

.home-content {
  max-width: 600px;
}

.home-content h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  opacity: 0;
  animation: slideBottom 1s ease forwards;
  animation-delay: .7s;
}

.home-content h3:nth-of-type(2) {
  margin-bottom: 30px;
  animation: slideTop 1s ease forwards;
  animation-delay: .7s;
}

.home-content h3 span {
  color: var(--accent-color);
}

.home-content h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin: -3px 0;
  opacity: 0;
  animation: slideRight 1s ease forwards;
  animation-delay: 1s;
}

.home-img img {
  max-width: 450px;
  border-radius: 50%;
  margin-right: -20px;
  box-shadow: 0 0 20px var(--accent-color);
  opacity: 0;
  animation: 
    zoomIn 1s ease forwards,
    floatImage 4s ease-in-out infinite;
  animation-delay: 2s, 3s;
}

.social-media a {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: transparent;
  border: 2px solid var(--accent-color);
  border-radius: 50%;
  color: var(--accent-color);
  font-size: 20px;
  text-decoration: none;
  margin: 30px 15px 30px 0;
  transition: 0.5s ease;
  opacity: 0;
  animation: slideLeft 1s ease forwards;
  animation-delay: calc(0.2s * var(--i));
}

.social-media a:hover {
  background: var(--accent-color);
  color: var(--main-bg-color);
  box-shadow: 0 0 20px var(--accent-color);
}

.btn {
  display: inline-block;
  padding: 12px 28px;
  background: var(--button-color);
  text-decoration: none;
  border-radius: 40px;
  box-shadow: 0 0 10px var(--shadow-color);
  font-size: 16px;
  color: var(--button-text-color);
  letter-spacing: 1px;
  font-weight: 600;
  transition: 0.5s ease;
  opacity: 0;
  animation: slideTop 1s ease forwards;
  animation-delay: 2s;
}

.btn:hover {
  box-shadow: 0 0 20px var(--accent-color);
}

.btn:active {
  background: none;
  color: var(--accent-color);
  border: 2px solid var(--accent-color);
}

/* Animations */
@keyframes slideRight {
  from { transform: translateX(-100px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideLeft {
  from { transform: translateX(100px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideTop {
  from { transform: translateY(100px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideBottom {
  from { transform: translateY(-100px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes zoomIn {
  from { transform: scale(0); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes floatImage {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-24px); }
}

.theme-toggle {
    position: fixed;
    top: 30px;
    right: 30px;
    background: var(--card-bg);
    border: none;
    color: var(--text);
    font-size: 28px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--card-shadow);
}

.theme-toggle:hover {
    transform: scale(1.1) rotate(15deg);
}
@media screen and (max-width: 1024px) {
  .header {
    padding: 20px 5%;
  }
  .navbar {
    position: static;
    width: 100%;
    display: none;
  }
  .navbar.active {
    display: block;
  }
  .navbar a {
    font-size: 16px;
    margin: 10px 0;
  }
}

@media screen and (max-width: 480px) {
  .navbar a {
    font-size: 14px;
    margin: 8px 0;
  }
}
@media screen and (max-width: 1024px) {
  .home {
    padding: 70px 5% 0;
    flex-direction: column-reverse;
    text-align: center;
  }
  .home-content {
    max-width: 100%;
  }
  .home-img img {
    max-width: 70%;
    margin-right: 0;
    margin-bottom: 30px;
  }
  .home-content h1 {
    font-size: 40px;
  }
  .home-content h3 {
    font-size: 24px;
  }
  .btn {
    font-size: 14px;
    padding: 10px 24px;
  }
}

@media screen and (max-width: 480px) {
  .home-content h1 {
    font-size: 32px;
  }
  .home-content h3 {
    font-size: 20px;
  }
}
.menu-icon {
  display: none;
  cursor: pointer;
  font-size: 24px;
}

@media screen and (max-width: 1024px) {
  .menu-icon {
    display: block;
  }
  .navbar {
    display: none;
  }
}

/* 移动端适配优化 */
@media screen and (max-width: 1024px) {
    /* 头部导航适配 */
    .header, .header2 {
        padding: 15px 5%;
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .logo {
        font-size: 36px;
        text-align: center;
    }

    .navbar {
        width: 100%;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 15px;
    }

    .navbar a {
        margin: 0;
        font-size: 16px;
        padding: 8px 15px;
        background: var(--card-bg);
        border-radius: 20px;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    /* 主页内容适配 */
    .home {
        padding: 100px 5% 0;
        flex-direction: column-reverse;
        text-align: center;
        gap: 30px;
    }

    .home-content {
        max-width: 100%;
        padding: 0 20px;
    }

    .home-content h1 {
        font-size: 40px;
    }

    .home-content h3 {
        font-size: 24px;
    }

    .home-img img {
        max-width: 70%;
        margin: 0 auto;
    }

    /* 主题切换按钮适配 */
    .theme-switch-wrapper {
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1000;
    }
}

/* 小屏幕设备适配 */
@media screen and (max-width: 480px) {
    .logo {
        font-size: 28px;
    }

    .navbar a {
        font-size: 14px;
        padding: 6px 12px;
    }

    .home-content h1 {
        font-size: 32px;
    }

    .home-content h3 {
        font-size: 20px;
    }

    .btn {
        padding: 10px 20px;
        font-size: 14px;
    }

    .home-img img {
        max-width: 85%;
    }

    /* 改进主题切换按钮在小屏幕上的显示 */
    .theme-switch-wrapper {
        padding: 3px 8px;
    }

    .theme-switch-label {
        font-size: 12px;
    }
}

/* 暗色模式下的移动端优化 */
.dark-mode .navbar a {
    background: var(--card-bg);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* 平板设备适配 */
@media screen and (min-width: 481px) and (max-width: 768px) {
    .logo {
        font-size: 32px;
    }

    .navbar a {
        font-size: 15px;
        padding: 7px 14px;
    }

    .home-content h1 {
        font-size: 36px;
    }

    .home-content h3 {
        font-size: 22px;
    }

    .home-img img {
        max-width: 75%;
    }
}

/* 横屏模式优化 */
@media screen and (max-height: 600px) and (orientation: landscape) {
    .home {
        padding-top: 80px;
        min-height: auto;
    }

    .home-img img {
        max-width: 50%;
    }

    .home-content {
        padding: 20px 0;
    }
}

/* 确内容不会被底部主题切换按钮遮挡 */
body {
    padding-bottom: 70px;
}

/* 添加平滑滚动效果 */
html {
    scroll-behavior: smooth;
}

/* 触摸设备的悬停效果优化 */
@media (hover: hover) {
    .navbar a:hover {
        transform: translateY(-2px);
    }

    .btn:hover {
        transform: translateY(-3px);
    }
}

/* 针对高分辨率设备的优化 */
@media screen and (min-width: 1440px) {
    .container {
        max-width: 1400px;
    }

    .home-content h1 {
        font-size: 64px;
    }

    .home-content h3 {
        font-size: 36px;
    }
}

/* 添加加载动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}