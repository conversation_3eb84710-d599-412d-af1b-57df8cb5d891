@import url(../../src/css/style.css);

:root {
	--bg-color: rgb(248, 246, 231);
    --text-color: #000000;
    --shadow-color: rgba(183, 178, 169, 0.2);
    --accent-color: #f6dc50;
    --card-bg: rgba(255, 255, 255, 0.8);
    --hover-transform: translateY(-10px);
    --transition-timing: 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.dark-mode {
    --bg-color: #1a1a1a;
    --text-color: #e0e0e0;
    --shadow-color: rgba(74, 74, 74, 0.3);
    --accent-color: #ffd700;
    --card-bg: rgba(44, 44, 44, 0.8);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Poppins", sans-serif;
}

body {
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: all 0.3s ease;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 120px auto 50px;
    padding: 0 20px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 40px;
}

.single-member {
    background: var(--card-bg);
    border-radius: 20px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px var(--shadow-color);
    transition: all var(--transition-timing);
    position: relative;
    min-height: 320px;
}

.single-member:hover {
    transform: var(--hover-transform);
    box-shadow: 0 15px 40px var(--shadow-color);
}

.member-image {
    position: relative;
    width: 100%;
    height: 220px;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-timing);
}

.single-member:hover .member-image img {
    transform: scale(1.1);
}

.member-info {
    padding: 20px;
    text-align: center;
    position: relative;
}

.member-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: var(--accent-color);
    transition: width 0.3s ease;
}

.single-member:hover .member-info::before {
    width: 100px;
}

.member-info h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    line-height: 1.3;
    margin-bottom: 8px;
    color: var(--text-color);
}

.member-info h5 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-base);
    color: var(--text-color);
    opacity: 0.8;
    margin-bottom: 15px;
}

.colored-text {
    background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.colored-text-2 {
    background: linear-gradient(45deg, #FFD93D, #FF6B6B);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.colored-text-3 {
    background: linear-gradient(45deg, #6C63FF, #FF6584);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.colored-text-4 {
    background: linear-gradient(45deg, #4ECDC4, #45B7D1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

@media (max-width: 768px) {
    .container {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 30px;
        padding: 0 15px;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.single-member {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
    animation-delay: calc(var(--animation-order) * 0.1s);
}

/* 
渐变文字效果使用方法:
1. 在 member-info 的 h3 标签中添加对��的类名
2. 例如: <h3 class="gradient-text pink-purple">成员名字</h3>
*/

/* 基础渐变文字效果 */
.gradient-text {
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-size: 200% auto;
    animation: shine 4s linear infinite;
}

/* 预设渐变色方案 */
.pink-purple {
    background-image: linear-gradient(45deg, #FF6B6B, #6C63FF);
}

.blue-green {
    background-image: linear-gradient(45deg, #4ECDC4, #45B7D1);
}

.yellow-orange {
    background-image: linear-gradient(45deg, #FFD93D, #FF6B6B);
}

.purple-pink {
    background-image: linear-gradient(45deg, #6C63FF, #FF6584);
}

.green-blue {
    background-image: linear-gradient(45deg, #2ecc71, #3498db);
}

.orange-red {
    background-image: linear-gradient(45deg, #f39c12, #e74c3c);
}

.blue-purple {
    background-image: linear-gradient(45deg, #3498db, #9b59b6);
}

.green-yellow {
    background-image: linear-gradient(45deg, #2ecc71, #f1c40f);
}

/* 彩虹渐变 */
.rainbow {
    background-image: linear-gradient(
        90deg,
        #ff0000,
        #ffa500,
        #ffff00,
        #008000,
        #0000ff,
        #4b0082,
        #ee82ee
    );
    background-size: 400% 100%;
    animation: rainbow 8s linear infinite;
}

/* 金属渐变 */
.metallic {
    background-image: linear-gradient(
        45deg,
        #BCC6CC,
        #EAEAEA,
        #BCC6CC,
        #EAEAEA
    );
    background-size: 200% 100%;
    animation: metallic 3s linear infinite;
}

/* 霓虹效果 */
.neon {
    text-shadow: 0 0 5px rgba(255,255,255,0.8),
                 0 0 10px rgba(255,255,255,0.5),
                 0 0 15px rgba(255,255,255,0.3);
    animation: neon 1.5s ease-in-out infinite alternate;
}

/* 动画效果 */
@keyframes shine {
    to {
        background-position: 200% center;
    }
}

@keyframes rainbow {
    0% { background-position: 0% 50%; }
    100% { background-position: 400% 50%; }
}

@keyframes metallic {
    to {
        background-position: 200% center;
    }
}

@keyframes neon {
    from {
        text-shadow: 0 0 5px rgba(255,255,255,0.8),
                     0 0 10px rgba(255,255,255,0.5),
                     0 0 15px rgba(255,255,255,0.3);
    }
    to {
        text-shadow: 0 0 10px rgba(255,255,255,0.9),
                     0 0 20px rgba(255,255,255,0.6),
                     0 0 30px rgba(255,255,255,0.4);
    }
}

/* 
使用方法示例:

1. 基础渐变色:
<h3 class="gradient-text pink-purple">成员名字</h3>

2. 彩虹效果:
<h3 class="gradient-text rainbow">成员名字</h3>

3. 金属效果:
<h3 class="gradient-text metallic">成员名字</h3>

4. 霓虹效果 (可以和渐变叠加):
<h3 class="gradient-text pink-purple neon">成员名字</h3>

5. 组合效果:
<h3 class="gradient-text rainbow neon">成员名字</h3>
*/

/* 暗色模式适配 */
.dark-mode .neon {
    text-shadow: 0 0 5px rgba(255,255,255,0.9),
                 0 0 10px rgba(255,255,255,0.6),
                 0 0 15px rgba(255,255,255,0.4);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .gradient-text {
        background-size: 150% auto;
    }
    
    .neon {
        text-shadow: 0 0 3px rgba(255,255,255,0.8),
                     0 0 6px rgba(255,255,255,0.5);
    }
}
