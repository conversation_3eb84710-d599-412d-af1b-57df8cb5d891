<!DOCTYPE html>
<html lang="zh_CN">
    <!-- 
    样式指南:
    1. 成员卡片样式类:
        - single-member: 基础卡片样式
        - effect-2: 添加悬停效果
        
    2. 成员信息样式:
        - member-image: 图片容器
        - member-info: 信息容器
        
    3. 文字渐变效果:
        基础用法: <h3 class="gradient-text [效果类名]">成员名字</h3>
        
        可用效果类名:
        - pink-purple: 粉紫渐变
        - blue-green: 蓝绿渐变
        - yellow-orange: 黄橙渐变
        - purple-pink: 紫粉渐变
        - green-blue: 绿蓝渐变
        - orange-red: 橙红渐变
        - blue-purple: 蓝紫渐变
        - green-yellow: 绿黄渐变
        - rainbow: 彩虹效果
        - metallic: 金属效果
        
    4. 特殊效果:
        - neon: 霓虹效果，可与渐变叠加
        示例: <h3 class="gradient-text rainbow neon">成员名字</h3>
        
    5. 暗色模式适配:
        所有效果已自动适配暗色模式
        
    6. 响应式设计:
        - 自动适应不同屏幕尺寸
        - 移动端优化显示效果
    -->
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="GHS人员名单">
    <meta name="keywords" content="GHS,Garbage Human Studio,Box world">  
    <!-- 样式表引入 -->
    <link rel="stylesheet" type="text/css" href="css/style.css">
    <link rel="stylesheet" href="../src/css/darklight.css">
    <link rel="icon" href="../src/images/ico.ico" type="image/x-icon">

    <title>GHS Staff</title>
    
    <!-- Howxm 统计脚本 -->
    <script>
        function _howxm() { _howxmQueue.push(arguments) }
        window._howxmQueue = window._howxmQueue || [];
        _howxm('setAppID', 'aa868da8-040c-48fd-bcb1-0c98b773bebf');
        (function () {
            var scriptId = 'howxm_script';
            if (!document.getElementById(scriptId)) {
                var e = document.createElement('script'),
                    t = document.getElementsByTagName('script')[0];
                e.setAttribute('id', scriptId);
                e.type = 'text/javascript'; e.async = !0;
                e.src = 'https://static.howxm.com/sdk.js';
                t.parentNode.insertBefore(e, t)
            }
        })();
    </script>
</head>

<body>
    <header class="header2">
        <a href="../index.html" class="logo">Garbage Human Studio</a>
        <nav class="navbar">
            <a href="../index.html" class="item" style="--i: 1">Home</a>
            <a href="#" class="active item" style="--i: 2">Staff</a>
            <a href="../project/index.html" class="item" style="--i: 2">Project</a>
            <a href="https://forum.ghs.red/" class="item" style="--i: 3">Forum</a>
        </nav>
    </header>
    <div class="theme-switch-wrapper">
        <span class="theme-switch-label">切换主题</span>
        <label class="theme-switch">
            <input type="checkbox" class="theme-switch__checkbox" id="themeSwitch">
            <div class="theme-switch__container">
                <div class="theme-switch__clouds"></div>
                <div class="theme-switch__stars-container">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 144 55" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M135.831 3.00688C135.055 3.85027 134.111 4.29946 133 4.35447C134.111 4.40947 135.055 4.85867 135.831 5.71123C136.607 6.55462 136.996 7.56303 136.996 8.72727C136.996 7.95722 137.172 7.25134 137.525 6.59129C137.886 5.93124 138.372 5.39954 138.98 5.00535C139.598 4.60199 140.268 4.39114 141 4.35447C139.88 4.2903 138.936 3.85027 138.16 3.00688C137.384 2.16348 136.996 1.16425 136.996 0C136.996 1.16425 136.607 2.16348 135.831 3.00688ZM31 23.3545C32.1114 23.2995 33.0551 22.8503 33.8313 22.0069C34.6075 21.1635 34.9956 20.1642 34.9956 19C34.9956 20.1642 35.3837 21.1635 36.1599 22.0069C36.9361 22.8503 37.8798 23.2903 39 23.3545C38.2679 23.3911 37.5976 23.602 36.9802 24.0053C36.3716 24.3995 35.8864 24.9312 35.5248 25.5913C35.172 26.2513 34.9956 26.9572 34.9956 27.7273C34.9956 26.563 34.6075 25.5546 33.8313 24.7112C33.0551 23.8587 32.1114 23.4095 31 23.3545ZM0 36.3545C1.11136 36.2995 2.05513 35.8503 2.83131 35.0069C3.6075 34.1635 3.99559 33.1642 3.99559 32C3.99559 33.1642 4.38368 34.1635 5.15987 35.0069C5.93605 35.8503 6.87982 36.2903 8 36.3545C7.26792 36.3911 6.59757 36.602 5.98015 37.0053C5.37155 37.3995 4.88644 37.9312 4.52481 38.5913C4.172 39.2513 3.99559 39.9572 3.99559 40.7273C3.99559 39.563 3.6075 38.5546 2.83131 37.7112C2.05513 36.8587 1.11136 36.4095 0 36.3545ZM56.8313 24.0069C56.0551 24.8503 55.1114 25.2995 54 25.3545C55.1114 25.4095 56.0551 25.8587 56.8313 26.7112C57.6075 27.5546 57.9956 28.563 57.9956 29.7273C57.9956 28.9572 58.172 28.2513 58.5248 27.5913C58.8864 26.9312 59.3716 26.3995 59.9802 26.0053C60.5976 25.602 61.2679 25.3911 62 25.3545C60.8798 25.2903 59.9361 24.8503 59.1599 24.0069C58.3837 23.1635 57.9956 22.1642 57.9956 21C57.9956 22.1642 57.6075 23.1635 56.8313 24.0069ZM81 25.3545C82.1114 25.2995 83.0551 24.8503 83.8313 24.0069C84.6075 23.1635 84.9956 22.1642 84.9956 21C84.9956 22.1642 85.3837 23.1635 86.1599 24.0069C86.9361 24.8503 87.8798 25.2903 89 25.3545C88.2679 25.3911 87.5976 25.602 86.9802 26.0053C86.3716 26.3995 85.8864 26.9312 85.5248 27.5913C85.172 28.2513 84.9956 28.9572 84.9956 29.7273C84.9956 28.563 84.6075 27.5546 83.8313 26.7112C83.0551 25.8587 82.1114 25.4095 81 25.3545ZM136 36.3545C137.111 36.2995 138.055 35.8503 138.831 35.0069C139.607 34.1635 139.996 33.1642 139.996 32C139.996 33.1642 140.384 34.1635 141.16 35.0069C141.936 35.8503 142.88 36.2903 144 36.3545C143.268 36.3911 142.598 36.602 141.98 37.0053C141.372 37.3995 140.886 37.9312 140.525 38.5913C140.172 39.2513 139.996 39.9572 139.996 40.7273C139.996 39.563 139.607 38.5546 138.831 37.7112C138.055 36.8587 137.111 36.4095 136 36.3545ZM101.831 49.0069C101.055 49.8503 100.111 50.2995 99 50.3545C100.111 50.4095 101.055 50.8587 101.831 51.7112C102.607 52.5546 102.996 53.563 102.996 54.7273C102.996 53.9572 103.172 53.2513 103.525 52.5913C103.886 51.9312 104.372 51.3995 104.98 51.0053C105.598 50.602 106.268 50.3911 107 50.3545C105.88 50.2903 104.936 49.8503 104.16 49.0069C103.384 48.1635 102.996 47.1642 102.996 46C102.996 47.1642 102.607 48.1635 101.831 49.0069Z"
                            fill="currentColor"></path>
                    </svg>
                </div>
                <div class="theme-switch__circle-container">
                    <div class="theme-switch__sun-moon-container">
                        <div class="theme-switch__moon">
                            <div class="theme-switch__spot"></div>
                            <div class="theme-switch__spot"></div>
                            <div class="theme-switch__spot"></div>
                        </div>
                    </div>
                </div>
            </div>
        </label>
    </div>
    <main class="container">
        <!-- Team members structure start -->
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="https://space.bilibili.com/551014476">
                    <img src="images/box.jpg" alt="Box World" loading="lazy">
                </a>
            </div>
            <div class="member-info">
                <h3 class="pink-text">Box World</h3>
                <h5>（</h5>
                <p></p>
            </div>
        </div>
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="https://www.mysticstars.cn">
                    <img src="https://bu.dusays.com/2024/06/08/66640a5af1910.png" alt="星星" loading="lazy">
                </a>
            </div>
            <div class="member-info">
                <h3><span class="colored-text">星星</span></h3>
                <h5>蒟蒻❤️</h5>
            </div>
        </div>
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="https://bilibili.com/space/662448670">
                    <img src="images/txt.jpg" alt="-TxT-" loading="lazy">
                </a>
            </div>
            <div class="member-info">
                <h3>-TxT-</h3>
                <h5>图一乐</h5>
            </div>
        </div>
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="https://www.ccw.site/student/5d8f054a483f20626dd412c1">
                    <img src="images/ipo_207.jpg" alt="Ipo_207" loading="lazy">
                </a>
            </div>
            <div class="member-info">
                <h3>Ipo_207</h3>
                <h5>我知道你很急，但请你先别急</h5>
            </div>
        </div>
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="https://www.ccw.site/student/5d63caf41c94e5686cb09114">
                    <img src="images/Cube.jpg" alt="Magic Cube" loading="lazy">
                </a>
            </div>
            <div class="member-info">
                <h3 class="colored-text-4">Magic Cube</h3>
                <h5>114514</h5>
            </div>
        </div>
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="https://space.bilibili.com/1058529366">
                    <img src="images/zzh.jpg" alt="zzh_周周" loading="lazy">
                </a>
            </div>
            <div class="member-info">
                <h3 class="colored-text-3">zzh_周周</h3>
                <h5>什么都想学什么都没学的半吊子</h5>
            </div>
        </div>
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="https://space.bilibili.com/1194045361">
                    <img src="images/358.jpg" alt="Member">
                </a>
            </div>
            <div class="member-info">
                <h3><span class="colored-text-2">358野战军2师</span></h3>
                <h5>fvv</h5>
            </div>
        </div>
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="https://www.luogu.com.cn/blog/patpowder-114514/">
                    <img src="images/paipai.jpg" alt="Member">
                </a>
            </div>
            <div class="member-info">
                <h3>拍粉酱</h3>
                <h5>GHS中的GH之最，不接受反驳</h5>
            </div>
        </div>
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="https://world.xiaomawang.com/w/person/project/all/1347431">
                    <img src="images/hjfunny.jpg" alt="Member">
                </a>
            </div>
            <div class="member-info">
                <h3>hjfunny</h3>
                <h5>唔唔唔螈神？999启动！</h5>
            </div>
        </div>
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="https://www.ccw.site/student/5d995d8e1c94e53c5ff9b9b3">
                    <img src="images/tian.jpg" alt="Member">
                </a>
            </div>
            <div class="member-info">
                <h3>天天TIAN</h3>
                <h5>Scer？啥也不是</h5>
            </div>
        </div>
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="https://www.ccw.site/student/5dde5cd23412bb2f6a829b18">
                    <img src="images/POCHA.jpg" alt="Member">
                </a>
            </div>
            <div class="member-info">
                <h3>POCHA</h3>
                <h5>游戏研究所：抛弃Sc，沉迷Ai</h5>
            </div>
        </div>
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="https://www.ccw.site/student/60837f4ea626c733fb66f29e">
                    <img src="images/money.jpg" alt="Member">
                </a>
            </div>
            <div class="member-info">
                <h3>MoneY</h3>
                <h5>半男半女的异形 喜欢重拳出击</h5>
            </div>
        </div>
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="https://space.bilibili.com/1755717124">
                    <img src="images/dreamspace.jpg" alt="Member">
                </a>
            </div>
            <div class="member-info">
                <h3>盗梦空间</h3>
                <h5>你干嘛，哎呦～ </h5>
            </div>
        </div>
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="https://www.ccw.site/student/5aec5123eb0a191592511468">
                    <img src="images/consols.jpg" alt="Member">
                </a>
            </div>
            <div class="member-info">
                <h3>索云Consolas</h3>
                <h5>一个中二/抽象/邪恶の不妙的猫</h5>
            </div>
        </div>
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="https://b23.tv/JB27j7H">
                    <img src="images/youshen.jpg" alt="Member">
                </a>
            </div>
            <div class="member-info">
                <h3>You_shen</h3>
                <h5>资深矮鲲两年半</h5>
            </div>
        </div>
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="https://www.luogu.com.cn/user/696174">
                    <img src="images/chm.gif" alt="Member">
                </a>
            </div>
            <div class="member-info">
                <h3>chm</h3>
                <h5>fwC艹信奥选手</h5>
            </div>
        </div>
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="http://www.jerrymc.cn/">
                    <img src="images/ray.jpg" alt="Member">
                </a>
            </div>
            <div class="member-info">
                <h3>bcmray</h3>
                <h5>请补充</h5>
            </div>
        </div>
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="https://listeneres.mysxl.cn/">
                    <img src="https://gp0.saobby.com/i/Lc5G0xpSovItSMOT.jpg" alt="Member">
                </a>
                <div class="member-info">
                    <h3>Listeneres</h3>
                    <h5>独立游戏开发屑</h5>
                </div>
            </div>
            <div class="member-info">
                <h3>Listeneres</h3>
                <h5>Final.</h5>
            </div>
        </div>
        <div class="single-member effect-2">
            <div class="member-image">
                <a href="https://chuishen.xyz/">
                    <img src="https://cravatar.cn/avatar/57ff1222a81493a0e72782530e77b269?s=500" alt="Member">
                </a>
            </div>
            <div class="member-info">
                <h3>氿月 Lafcadia</h3>
                <h5>Founder of Team OblivionOcean.</h5>
            </div>
        </div>
    </main>

    <script src="../src/js/darkmode.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', () => {
        // 为每个成员卡片添加动画序号
        document.querySelectorAll('.single-member').forEach((card, index) => {
            card.style.setProperty('--animation-order', index + 1);
        });
    });
    </script>

</body>

</html>