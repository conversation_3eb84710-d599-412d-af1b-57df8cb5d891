<html>

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="icon" href="src\images\ico.ico" type="image/x-icon">
  <title>404 - Page Not Found</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap');
    @import url(/src/css/font.css);

    :root {
      --card-bg: rgba(255, 255, 255, 0.8);
      --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      --text-color: #000;
    }

    .dark-mode {
      --text-color: #E0E0E0;
      --bg-color: #1A1A1A;
      --shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      --card-bg: rgba(40, 40, 40, 0.8);
    }

    .theme-switch {
      --toggle-size: 16px;
      --container-width: 5.625em;
      --container-height: 2.5em;
      --container-radius: 6.25em;
      --container-light-bg: #3D7EAE;
      --container-night-bg: #1D1F2C;
      --circle-container-diameter: 3.375em;
      --sun-moon-diameter: 2.125em;
      --sun-bg: #ECCA2F;
      --moon-bg: #C4C9D1;
      --spot-color: #959DB1;
      --circle-container-offset: calc((var(--circle-container-diameter) - var(--container-height)) / 2 * -1);
      --stars-color: #fff;
      --clouds-color: #F3FDFF;
      --back-clouds-color: #AACADF;
      --transition: .5s cubic-bezier(0, -0.02, 0.4, 1.25);
      --circle-transition: .3s cubic-bezier(0, -0.02, 0.35, 1.17);
    }

    .theme-switch,
    .theme-switch *,
    .theme-switch *::before,
    .theme-switch *::after {
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      margin: 0;
      padding: 0;
      font-size: var(--toggle-size);
    }

    .theme-switch__container {
      width: var(--container-width);
      height: var(--container-height);
      background-color: var(--container-light-bg);
      border-radius: var(--container-radius);
      overflow: hidden;
      cursor: pointer;
      -webkit-box-shadow: 0em -0.062em 0.062em rgba(0, 0, 0, 0.25), 0em 0.062em 0.125em rgba(255, 255, 255, 0.94);
      box-shadow: 0em -0.062em 0.062em rgba(0, 0, 0, 0.25), 0em 0.062em 0.125em rgba(255, 255, 255, 0.94);
      -webkit-transition: var(--transition);
      -o-transition: var(--transition);
      transition: var(--transition);
      position: relative;
    }

    .theme-switch__container::before {
      content: "";
      position: absolute;
      z-index: 1;
      inset: 0;
      -webkit-box-shadow: 0em 0.05em 0.187em rgba(0, 0, 0, 0.25) inset, 0em 0.05em 0.187em rgba(0, 0, 0, 0.25) inset;
      box-shadow: 0em 0.05em 0.187em rgba(0, 0, 0, 0.25) inset, 0em 0.05em 0.187em rgba(0, 0, 0, 0.25) inset;
      border-radius: var(--container-radius)
    }

    .theme-switch__checkbox {
      display: none;
    }

    .theme-switch__circle-container {
      width: var(--circle-container-diameter);
      height: var(--circle-container-diameter);
      background-color: rgba(255, 255, 255, 0.1);
      position: absolute;
      left: var(--circle-container-offset);
      top: var(--circle-container-offset);
      border-radius: var(--container-radius);
      -webkit-box-shadow: inset 0 0 0 3.375em rgba(255, 255, 255, 0.1), inset 0 0 0 3.375em rgba(255, 255, 255, 0.1), 0 0 0 0.625em rgba(255, 255, 255, 0.1), 0 0 0 1.25em rgba(255, 255, 255, 0.1);
      box-shadow: inset 0 0 0 3.375em rgba(255, 255, 255, 0.1), inset 0 0 0 3.375em rgba(255, 255, 255, 0.1), 0 0 0 0.625em rgba(255, 255, 255, 0.1), 0 0 0 1.25em rgba(255, 255, 255, 0.1);
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-transition: var(--circle-transition);
      -o-transition: var(--circle-transition);
      transition: var(--circle-transition);
      pointer-events: none;
    }

    .theme-switch__sun-moon-container {
      pointer-events: auto;
      position: relative;
      z-index: 2;
      width: var(--sun-moon-diameter);
      height: var(--sun-moon-diameter);
      margin: auto;
      border-radius: var(--container-radius);
      background-color: var(--sun-bg);
      -webkit-box-shadow: 0.062em 0.062em 0.062em 0em rgba(254, 255, 239, 0.61) inset, 0em -0.062em 0.062em 0em #a1872a inset;
      box-shadow: 0.062em 0.062em 0.062em 0em rgba(254, 255, 239, 0.61) inset, 0em -0.062em 0.062em 0em #a1872a inset;
      -webkit-filter: drop-shadow(0.062em 0.125em 0.125em rgba(0, 0, 0, 0.25)) drop-shadow(0em 0.062em 0.125em rgba(0, 0, 0, 0.25));
      filter: drop-shadow(0.062em 0.125em 0.125em rgba(0, 0, 0, 0.25)) drop-shadow(0em 0.062em 0.125em rgba(0, 0, 0, 0.25));
      overflow: hidden;
      -webkit-transition: var(--transition);
      -o-transition: var(--transition);
      transition: var(--transition);
    }

    .theme-switch__moon {
      -webkit-transform: translateX(100%);
      -ms-transform: translateX(100%);
      transform: translateX(100%);
      width: 100%;
      height: 100%;
      background-color: var(--moon-bg);
      border-radius: inherit;
      -webkit-box-shadow: 0.062em 0.062em 0.062em 0em rgba(254, 255, 239, 0.61) inset, 0em -0.062em 0.062em 0em #969696 inset;
      box-shadow: 0.062em 0.062em 0.062em 0em rgba(254, 255, 239, 0.61) inset, 0em -0.062em 0.062em 0em #969696 inset;
      -webkit-transition: var(--transition);
      -o-transition: var(--transition);
      transition: var(--transition);
      position: relative;
    }

    .theme-switch__spot {
      position: absolute;
      top: 0.75em;
      left: 0.312em;
      width: 0.75em;
      height: 0.75em;
      border-radius: var(--container-radius);
      background-color: var(--spot-color);
      -webkit-box-shadow: 0em 0.0312em 0.062em rgba(0, 0, 0, 0.25) inset;
      box-shadow: 0em 0.0312em 0.062em rgba(0, 0, 0, 0.25) inset;
    }

    .theme-switch__spot:nth-of-type(2) {
      width: 0.375em;
      height: 0.375em;
      top: 0.937em;
      left: 1.375em;
    }

    .theme-switch__spot:nth-last-of-type(3) {
      width: 0.25em;
      height: 0.25em;
      top: 0.312em;
      left: 0.812em;
    }

    .theme-switch__clouds {
      width: 1.25em;
      height: 1.25em;
      background-color: var(--clouds-color);
      border-radius: var(--container-radius);
      position: absolute;
      bottom: -0.625em;
      left: 0.312em;
      -webkit-box-shadow: 0.937em 0.312em var(--clouds-color), -0.312em -0.312em var(--back-clouds-color), 1.437em 0.375em var(--clouds-color), 0.5em -0.125em var(--back-clouds-color), 2.187em 0 var(--clouds-color), 1.25em -0.062em var(--back-clouds-color), 2.937em 0.312em var(--clouds-color), 2em -0.312em var(--back-clouds-color), 3.625em -0.062em var(--clouds-color), 2.625em 0em var(--back-clouds-color), 4.5em -0.312em var(--clouds-color), 3.375em -0.437em var(--back-clouds-color), 4.625em -1.75em 0 0.437em var(--clouds-color), 4em -0.625em var(--back-clouds-color), 4.125em -2.125em 0 0.437em var(--back-clouds-color);
      box-shadow: 0.937em 0.312em var(--clouds-color), -0.312em -0.312em var(--back-clouds-color), 1.437em 0.375em var(--clouds-color), 0.5em -0.125em var(--back-clouds-color), 2.187em 0 var(--clouds-color), 1.25em -0.062em var(--back-clouds-color), 2.937em 0.312em var(--clouds-color), 2em -0.312em var(--back-clouds-color), 3.625em -0.062em var(--clouds-color), 2.625em 0em var(--back-clouds-color), 4.5em -0.312em var(--clouds-color), 3.375em -0.437em var(--back-clouds-color), 4.625em -1.75em 0 0.437em var(--clouds-color), 4em -0.625em var(--back-clouds-color), 4.125em -2.125em 0 0.437em var(--back-clouds-color);
      -webkit-transition: 0.5s cubic-bezier(0, -0.02, 0.4, 1.25);
      -o-transition: 0.5s cubic-bezier(0, -0.02, 0.4, 1.25);
      transition: 0.5s cubic-bezier(0, -0.02, 0.4, 1.25);
    }

    .theme-switch__stars-container {
      position: absolute;
      color: var(--stars-color);
      top: -100%;
      left: 0.312em;
      width: 2.75em;
      height: auto;
      -webkit-transition: var(--transition);
      -o-transition: var(--transition);
      transition: var(--transition);
    }

    /* actions */

    .theme-switch__checkbox:checked+.theme-switch__container {
      background-color: var(--container-night-bg);
    }

    .theme-switch__checkbox:checked+.theme-switch__container .theme-switch__circle-container {
      left: calc(100% - var(--circle-container-offset) - var(--circle-container-diameter));
    }

    .theme-switch__checkbox:checked+.theme-switch__container .theme-switch__circle-container:hover {
      left: calc(100% - var(--circle-container-offset) - var(--circle-container-diameter) - 0.187em)
    }

    .theme-switch__circle-container:hover {
      left: calc(var(--circle-container-offset) + 0.187em);
    }

    .theme-switch__checkbox:checked+.theme-switch__container .theme-switch__moon {
      -webkit-transform: translate(0);
      -ms-transform: translate(0);
      transform: translate(0);
    }

    .theme-switch__checkbox:checked+.theme-switch__container .theme-switch__clouds {
      bottom: -4.062em;
    }

    .theme-switch__checkbox:checked+.theme-switch__container .theme-switch__stars-container {
      top: 50%;
      -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
      transform: translateY(-50%);
    }

    /* 新增样式 */
    .theme-switch-wrapper {
      position: fixed;
      bottom: 20px;
      left: 20px;
      z-index: 1000;
      display: flex;
      align-items: center;
      background-color: var(--card-bg);
      padding: 5px 10px;
      border-radius: 20px;
      box-shadow: var(--shadow);
    }

    .theme-switch-label {
      margin-right: 10px;
      font-size: 0.9rem;
      color: var(--text-color);
    }

    :root {
      --primary-color: #ffc91a;
      --secondary-color: #FF9800;
      --text-color: #333;
      --background-color: rgb(248, 246, 231);
      --container-bg: rgba(255, 255, 255, 0.4);
      --btn-hover-color: #ffffff;
      --btn-shadow: rgba(255, 193, 7, 0.4);
      --btn-hover-shadow: rgba(255, 152, 0, 0.4);
      --particle-opacity: 0.6;
    }

    .dark-mode {
      --primary-color: #ffc918;
      --secondary-color: #FFB74D;
      --text-color: #ffffff;
      --background-color: #1a1a1a;
      --container-bg: rgba(30, 30, 30, 0.4);
      --btn-hover-color: #333;
      --btn-shadow: rgba(255, 193, 7, 0.2);
      --btn-hover-shadow: rgba(255, 152, 0, 0.2);
      --particle-opacity: 0.3;
    }

    body {
      font-family: 'Poppins', sans-serif;
      background-color: var(--background-color);
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      color: var(--text-color);
      overflow: hidden;
      transition: background-color 0.3s, color 0.3s;
    }

    .container {
      text-align: center;
      background: var(--container-bg);
      border-radius: 20px;
      padding: 60px;
      max-width: 500px;
      width: 90%;
      position: relative;
      z-index: 1;
      backdrop-filter: blur(15px);
      animation: fadeIn 1s ease-out, float 6s ease-in-out infinite;
      transition: background-color 0.3s;
    }

    .error-icon {
      width: 150px;
      height: 150px;
      margin-bottom: 30px;
      filter: drop-shadow(0 10px 15px var(--btn-shadow));
      transition: filter 0.3s;
    }

    h1 {
      font-size: 42px;
      font-weight: 600;
      color: var(--secondary-color);
      margin: 0 0 20px;
      letter-spacing: -1px;
      animation: slideInFromLeft 1s ease-out;
      transition: color 0.3s;
    }

    p {
      font-size: 18px;
      color: var(--text-color);
      margin: 0 0 30px;
      line-height: 1.6;
      opacity: 0.8;
      animation: slideInFromRight 1s ease-out;
      transition: color 0.3s;
    }

    .btn {
      display: inline-block;
      padding: 14px 28px;
      background-color: var(--primary-color);
      color: var(--text-color);
      text-decoration: none;
      border-radius: 30px;
      font-weight: 500;
      font-size: 16px;
      transition: all 0.3s ease;
      box-shadow: 0 5px 15px var(--btn-shadow);
      position: relative;
      overflow: hidden;
      animation: popIn 0.5s ease-out 0.5s both;
    }

    .btn:hover {
      background-color: var(--secondary-color);
      color: var(--btn-hover-color);
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 8px 20px var(--btn-hover-shadow);
    }

    .btn::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 5px;
      height: 5px;
      background: rgba(255, 255, 255, 0.5);
      opacity: 0;
      border-radius: 100%;
      transform: scale(1, 1) translate(-50%);
      transform-origin: 50% 50%;
    }

    .btn:hover::after {
      animation: ripple 1s ease-out;
    }

    .background {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 0;
      overflow: hidden;
    }

    .particle {
      position: absolute;
      border-radius: 50%;
      background: var(--primary-color);
      pointer-events: none;
      opacity: var(--particle-opacity);
      transition: background-color 0.3s, opacity 0.3s;
    }

    @keyframes float {

      0%,
      100% {
        transform: translateY(0);
      }

      50% {
        transform: translateY(-20px);
      }
    }

    @keyframes pulse {

      0%,
      100% {
        transform: scale(1);
      }

      50% {
        transform: scale(1.1);
      }
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
      }

      to {
        opacity: 1;
      }
    }

    @keyframes slideInFromLeft {
      from {
        transform: translateX(-50px);
        opacity: 0;
      }

      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    @keyframes slideInFromRight {
      from {
        transform: translateX(50px);
        opacity: 0;
      }

      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    @keyframes popIn {
      from {
        transform: scale(0.8);
        opacity: 0;
      }

      to {
        transform: scale(1);
        opacity: 1;
      }
    }

    @keyframes ripple {
      0% {
        transform: scale(0, 0);
        opacity: 0.5;
      }

      100% {
        transform: scale(20, 20);
        opacity: 0;
      }
    }

    @keyframes rotate {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }

    @keyframes float {

      0%,
      100% {
        transform: translateY(0);
      }

      50% {
        transform: translateY(-10px);
      }
    }
  </style>
</head>

<body>
  <div class="theme-switch-wrapper">
    <span class="theme-switch-label">切换主题</span>
    <label class="theme-switch">
      <input type="checkbox" class="theme-switch__checkbox" id="themeSwitch">
      <div class="theme-switch__container">
        <div class="theme-switch__clouds"></div>
        <div class="theme-switch__stars-container">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 144 55" fill="none">
            <path fill-rule="evenodd" clip-rule="evenodd"
              d="M135.831 3.00688C135.055 3.85027 134.111 4.29946 133 4.35447C134.111 4.40947 135.055 4.85867 135.831 5.71123C136.607 6.55462 136.996 7.56303 136.996 8.72727C136.996 7.95722 137.172 7.25134 137.525 6.59129C137.886 5.93124 138.372 5.39954 138.98 5.00535C139.598 4.60199 140.268 4.39114 141 4.35447C139.88 4.2903 138.936 3.85027 138.16 3.00688C137.384 2.16348 136.996 1.16425 136.996 0C136.996 1.16425 136.607 2.16348 135.831 3.00688ZM31 23.3545C32.1114 23.2995 33.0551 22.8503 33.8313 22.0069C34.6075 21.1635 34.9956 20.1642 34.9956 19C34.9956 20.1642 35.3837 21.1635 36.1599 22.0069C36.9361 22.8503 37.8798 23.2903 39 23.3545C38.2679 23.3911 37.5976 23.602 36.9802 24.0053C36.3716 24.3995 35.8864 24.9312 35.5248 25.5913C35.172 26.2513 34.9956 26.9572 34.9956 27.7273C34.9956 26.563 34.6075 25.5546 33.8313 24.7112C33.0551 23.8587 32.1114 23.4095 31 23.3545ZM0 36.3545C1.11136 36.2995 2.05513 35.8503 2.83131 35.0069C3.6075 34.1635 3.99559 33.1642 3.99559 32C3.99559 33.1642 4.38368 34.1635 5.15987 35.0069C5.93605 35.8503 6.87982 36.2903 8 36.3545C7.26792 36.3911 6.59757 36.602 5.98015 37.0053C5.37155 37.3995 4.88644 37.9312 4.52481 38.5913C4.172 39.2513 3.99559 39.9572 3.99559 40.7273C3.99559 39.563 3.6075 38.5546 2.83131 37.7112C2.05513 36.8587 1.11136 36.4095 0 36.3545ZM56.8313 24.0069C56.0551 24.8503 55.1114 25.2995 54 25.3545C55.1114 25.4095 56.0551 25.8587 56.8313 26.7112C57.6075 27.5546 57.9956 28.563 57.9956 29.7273C57.9956 28.9572 58.172 28.2513 58.5248 27.5913C58.8864 26.9312 59.3716 26.3995 59.9802 26.0053C60.5976 25.602 61.2679 25.3911 62 25.3545C60.8798 25.2903 59.9361 24.8503 59.1599 24.0069C58.3837 23.1635 57.9956 22.1642 57.9956 21C57.9956 22.1642 57.6075 23.1635 56.8313 24.0069ZM81 25.3545C82.1114 25.2995 83.0551 24.8503 83.8313 24.0069C84.6075 23.1635 84.9956 22.1642 84.9956 21C84.9956 22.1642 85.3837 23.1635 86.1599 24.0069C86.9361 24.8503 87.8798 25.2903 89 25.3545C88.2679 25.3911 87.5976 25.602 86.9802 26.0053C86.3716 26.3995 85.8864 26.9312 85.5248 27.5913C85.172 28.2513 84.9956 28.9572 84.9956 29.7273C84.9956 28.563 84.6075 27.5546 83.8313 26.7112C83.0551 25.8587 82.1114 25.4095 81 25.3545ZM136 36.3545C137.111 36.2995 138.055 35.8503 138.831 35.0069C139.607 34.1635 139.996 33.1642 139.996 32C139.996 33.1642 140.384 34.1635 141.16 35.0069C141.936 35.8503 142.88 36.2903 144 36.3545C143.268 36.3911 142.598 36.602 141.98 37.0053C141.372 37.3995 140.886 37.9312 140.525 38.5913C140.172 39.2513 139.996 39.9572 139.996 40.7273C139.996 39.563 139.607 38.5546 138.831 37.7112C138.055 36.8587 137.111 36.4095 136 36.3545ZM101.831 49.0069C101.055 49.8503 100.111 50.2995 99 50.3545C100.111 50.4095 101.055 50.8587 101.831 51.7112C102.607 52.5546 102.996 53.563 102.996 54.7273C102.996 53.9572 103.172 53.2513 103.525 52.5913C103.886 51.9312 104.372 51.3995 104.98 51.0053C105.598 50.602 106.268 50.3911 107 50.3545C105.88 50.2903 104.936 49.8503 104.16 49.0069C103.384 48.1635 102.996 47.1642 102.996 46C102.996 47.1642 102.607 48.1635 101.831 49.0069Z"
              fill="currentColor"></path>
          </svg>
        </div>
        <div class="theme-switch__circle-container">
          <div class="theme-switch__sun-moon-container">
            <div class="theme-switch__moon">
              <div class="theme-switch__spot"></div>
              <div class="theme-switch__spot"></div>
              <div class="theme-switch__spot"></div>
            </div>
          </div>
        </div>
      </div>
    </label>
  </div>
  <div class="background" id="particleContainer"></div>
  <div class="rotating-bg"></div>

  <div class="container">
    <svg class="error-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#FFC107">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z" />
    </svg>
    <h1>404: Page Not Found</h1>
    <p>Oops! 您好像迷路了，别担心，我们这就引领你回去！</p>
    <a href="https://www.ghs.red" class="btn">返回主页</a>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const icon = document.querySelector('.error-icon1');
      gsap.to(icon, { duration: 2, rotation: 360, scale: 1.1, yoyo: true, repeat: -1, ease: "power1.inOut" });

      // 粒子动画
      const particleContainer = document.getElementById('particleContainer');
      const particleCount = 150;


      for (let i = 0; i < particleCount; i++) {
        createParticle();
      }
      function createParticle() {
        const particle = document.createElement('div');
        particle.classList.add('particle');

        const size = Math.random() * 25 + 8;
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;
        const startPositionX = Math.random() * (window.innerWidth + 400) - 150;
        const startPositionY = Math.random() * (window.innerHeight + 400) - 150;
        particle.style.left = `${startPositionX}px`;
        particle.style.top = `${startPositionY}px`;

        gsap.to(particle, {
          duration: Math.random() * 20 + 10,
          repeat: -1,
          yoyo: true,
          ease: "power1.inOut"
        });


        particleContainer.appendChild(particle);
      }

      // 文本动画
      const text = document.querySelector('p');
      const words = text.textContent.split(' ');
      text.innerHTML = words.map(word => `<span>${word}</span>`).join(' ');

      gsap.from('p span', {
        opacity: 0,
        y: 20,
        duration: 0.5,
        stagger: 0.1,
        ease: "back.out"
      });

      // 按钮动画
      const btn = document.querySelector('.btn');
      btn.addEventListener('mouseenter', () => {
        gsap.to(btn, { duration: 0.3, scale: 1.1, ease: "back.out" });
      });
      btn.addEventListener('mouseleave', () => {
        gsap.to(btn, { duration: 0.3, scale: 1, ease: "back.out" });
      });
    });

    // 交互式背景
    document.addEventListener('mousemove', (e) => {
      const particles = document.querySelectorAll('.particle');
      particles.forEach(particle => {
        const speed = particle.offsetWidth / 10;
        const x = (window.innerWidth / 2 - e.clientX) / speed;
        const y = (window.innerHeight / 2 - e.clientY) / speed;
        gsap.to(particle, { duration: 0.5, x: x, y: y, ease: "power2.out" });
      });
    });
    // 明暗模式切换
    const themeSwitch = document.getElementById('themeSwitch');
    const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)');

    function setTheme(theme) {
      document.body.classList.toggle('dark-mode', theme === 'dark');
      localStorage.setItem('theme', theme);
    }

    // 检查本地存储或系统偏好
    const savedTheme = localStorage.getItem('theme') || (prefersDarkScheme.matches ? 'dark' : 'light');
    setTheme(savedTheme);
    themeSwitch.checked = savedTheme === 'dark';

    themeSwitch.addEventListener('change', () => {
      const currentTheme = themeSwitch.checked ? 'dark' : 'light';
      setTheme(currentTheme);
    });

    // 监听系统主题变化
    prefersDarkScheme.addEventListener('change', (e) => {
      const newTheme = e.matches ? 'dark' : 'light';
      setTheme(newTheme);
      themeSwitch.checked = newTheme === 'dark';
    });
  </script>
</body>

</html>